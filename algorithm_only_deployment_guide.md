# 算法功能裁剪部署指南

## 1. 裁剪目标

将完整的仿真系统裁剪为纯算法功能，去除所有仿真相关组件，保留核心的路径规划和地形分析算法，适用于实际机器人平台部署。

## 2. 模块裁剪方案

### 2.1 保留模块 (核心算法)

#### A. local_planner (必须保留)
**功能**: 局部路径规划核心算法
**保留原因**: 
- 包含预计算路径集合和实时路径选择算法
- 网格化碰撞检测系统
- 多尺度路径搜索和评分机制

**需要修改的部分**:
```cpp
// 移除仿真相关的调试可视化
#define PLOTPATHSET 0  // 改为0，关闭路径可视化

// 保留核心算法接口
auto subOdometry = nh->create_subscription<nav_msgs::msg::Odometry>("/state_estimation", 5, odometryHandler);
auto subLaserCloud = nh->create_subscription<sensor_msgs::msg::PointCloud2>("/registered_scan", 5, laserCloudHandler);
auto pubPath = nh->create_publisher<nav_msgs::msg::Path>("/path", 5);
```

#### B. terrain_analysis (必须保留)
**功能**: 地形可通行性分析
**保留原因**:
- 3D体素化地形表示
- 实时障碍物检测和分类
- 动态地形更新算法

**需要修改的部分**:
```cpp
// 保留核心地形分析功能
auto subOdometry = nh->create_subscription<nav_msgs::msg::Odometry>("/state_estimation", 5, odometryHandler);
auto subLaserCloud = nh->create_subscription<sensor_msgs::msg::PointCloud2>("/registered_scan", 5, laserCloudHandler);
auto pubLaserCloud = nh->create_publisher<sensor_msgs::msg::PointCloud2>("/terrain_map", 2);
```

#### C. terrain_analysis_ext (可选保留)
**功能**: 扩展地形分析功能
**保留条件**: 如果需要更高级的地形分析功能
**简化方案**: 可以合并到terrain_analysis中

### 2.2 完全移除模块

#### A. vehicle_simulator (完全移除)
**移除原因**: 
- 纯仿真功能，包含Gazebo集成
- 虚拟车辆物理模拟
- 仿真传感器数据生成

#### B. sensor_scan_generation (大部分移除)
**移除部分**:
- 仿真传感器数据生成
- 虚拟扫描合成

**可保留部分**:
- 传感器数据同步功能(如果需要)

#### C. velodyne_simulator (完全移除)
**移除原因**: 
- Velodyne激光雷达仿真插件
- Gazebo相关依赖

#### D. visualization_tools (部分移除)
**移除部分**:
- 仿真环境可视化
- 虚拟轨迹生成

**保留部分**:
- 实际数据可视化功能
- 性能监控工具

## 3. 依赖关系裁剪

### 3.1 CMakeLists.txt 修改

#### 移除Gazebo依赖:
```cmake
# 移除这些依赖
# find_package(gazebo_msgs REQUIRED)
# find_package(gazebo_ros REQUIRED)
# find_package(gazebo_plugins REQUIRED)

# 保留核心依赖
find_package(rclcpp REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(pcl_ros REQUIRED)
find_package(pcl_conversions REQUIRED)
```

#### 修改编译目标:
```cmake
# 只编译算法相关节点
add_executable(localPlanner src/localPlanner.cpp)
add_executable(terrainAnalysis src/terrainAnalysis.cpp)
add_executable(pathFollower src/pathFollower.cpp)

# 移除仿真相关编译目标
# add_executable(vehicleSimulator src/vehicleSimulator.cpp)
```

### 3.2 package.xml 修改

```xml
<!-- 移除仿真依赖 -->
<!-- <depend>gazebo_msgs</depend> -->
<!-- <depend>gazebo_ros</depend> -->
<!-- <depend>gazebo_plugins</depend> -->

<!-- 保留核心依赖 -->
<depend>rclcpp</depend>
<depend>sensor_msgs</depend>
<depend>nav_msgs</depend>
<depend>geometry_msgs</depend>
<depend>pcl_ros</depend>
<depend>pcl_conversions</depend>
<depend>tf2</depend>
<depend>tf2_ros</depend>
```

## 4. 启动文件重构

### 4.1 创建算法专用启动文件

```python
# algorithm_only.launch.py
from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    return LaunchDescription([
        # 声明参数
        DeclareLaunchArgument('pathFolder', default_value='/path/to/paths'),
        DeclareLaunchArgument('vehicleHeight', default_value='0.75'),
        
        # 核心算法节点
        Node(
            package='local_planner',
            executable='localPlanner',
            name='localPlanner',
            parameters=[{
                'pathFolder': LaunchConfiguration('pathFolder'),
                'vehicleHeight': LaunchConfiguration('vehicleHeight'),
                'useTerrainAnalysis': True,
                'checkObstacle': True,
            }]
        ),
        
        Node(
            package='terrain_analysis',
            executable='terrainAnalysis',
            name='terrainAnalysis',
            parameters=[{
                'scanVoxelSize': 0.05,
                'terrainVoxelSize': 0.2,
                'vehicleHeight': LaunchConfiguration('vehicleHeight'),
            }]
        ),
        
        Node(
            package='local_planner',
            executable='pathFollower',
            name='pathFollower',
            parameters=[{
                'maxSpeed': 2.0,
                'autonomyMode': True,
            }]
        ),
        
        # 可选: 数据接口节点
        Node(
            package='loam_interface',
            executable='loamInterface',
            name='loamInterface',
            parameters=[{
                'stateEstimationTopic': '/odometry/filtered',
                'registeredScanTopic': '/velodyne_points',
            }]
        ),
    ])
```

### 4.2 移除的启动组件

```python
# 这些组件不再启动
# start_vehicle_simulator
# start_sensor_scan_generation (仿真部分)
# start_gazebo
# start_rviz (可选择性保留)
```

## 5. 配置参数调整

### 5.1 关闭仿真相关功能

```yaml
# local_planner参数
useTerrainAnalysis: true      # 启用地形分析
checkObstacle: true           # 启用障碍物检测
autonomyMode: true            # 自主模式
pathScaleBySpeed: true        # 根据速度调整路径

# terrain_analysis参数  
clearingCloud: false          # 关闭仿真清理功能
noDataObstacle: false         # 关闭无数据障碍物
adjustZ: false                # 关闭Z轴自动调整(仿真功能)
adjustIncl: false             # 关闭倾斜调整(仿真功能)

# 传感器参数(根据实际硬件调整)
vehicleHeight: 0.75           # 实际车辆高度
sensorOffsetX: 0.0            # 实际传感器X偏移
sensorOffsetY: 0.0            # 实际传感器Y偏移
```

### 5.2 实际硬件适配参数

```yaml
# 激光雷达参数
laserVoxelSize: 0.05          # 根据激光雷达精度调整
adjacentRange: 3.5            # 根据激光雷达范围调整
minRelZ: -0.5                 # 根据地面情况调整
maxRelZ: 0.25                 # 根据车辆高度调整

# 车辆运动学参数
vehicleLength: 0.6            # 实际车辆长度
vehicleWidth: 0.6             # 实际车辆宽度
maxSpeed: 2.0                 # 最大安全速度
```

## 6. 数据接口适配

### 6.1 输入数据源修改

```cpp
// 原仿真数据源 → 实际传感器数据源
"/velodyne_points"     → "/velodyne_points"        // 实际激光雷达
"/state_estimation"    → "/odometry/filtered"      // 实际SLAM输出
"/joy"                → "/joy"                     // 实际手柄(可选)
```

### 6.2 输出接口保持

```cpp
// 这些输出接口保持不变，供下游使用
"/terrain_map"         // 地形图输出
"/path"               // 规划路径输出  
"/cmd_vel"            // 速度控制输出
```

## 7. 编译和部署步骤

### 7.1 清理仿真组件

```bash
# 删除仿真相关包
rm -rf src/vehicle_simulator
rm -rf src/velodyne_simulator
rm -rf src/sensor_scan_generation  # 或选择性保留

# 清理编译缓存
rm -rf build/ install/ log/
```

### 7.2 重新编译

```bash
# 只编译算法相关包
colcon build --packages-select local_planner terrain_analysis loam_interface

# 或编译所有保留的包
colcon build --packages-ignore vehicle_simulator velodyne_simulator
```

### 7.3 测试部署

```bash
# 启动算法节点
ros2 launch local_planner algorithm_only.launch.py

# 检查节点状态
ros2 node list
ros2 topic list

# 监控算法性能
ros2 topic hz /terrain_map
ros2 topic hz /path
```

## 8. 验证和调试

### 8.1 功能验证清单

- [ ] 地形分析模块正常接收点云数据
- [ ] 路径规划器能够生成有效路径
- [ ] 路径跟踪控制器响应正常
- [ ] 算法实时性满足要求(>10Hz)
- [ ] 内存使用在合理范围内

### 8.2 性能监控

```bash
# 监控话题频率
ros2 topic hz /terrain_map    # 应该>2Hz
ros2 topic hz /path          # 应该>10Hz

# 监控计算延迟
ros2 topic delay /registered_scan /terrain_map
ros2 topic delay /terrain_map /path

# 监控系统资源
htop  # 查看CPU和内存使用
```

这个裁剪方案可以将完整系统的复杂度降低约60-70%，同时保留所有核心算法功能，非常适合在实际机器人平台上部署。
