# 自主探索系统架构分析文档

## 1. 系统概述

这是一个基于ROS2的地面机器人自主导航和探索系统，主要用于复杂地形环境下的路径规划和避障。系统采用模块化设计，包含感知、规划、控制和仿真等核心组件。

## 2. 核心模块架构

### 2.1 模块组成
- **local_planner**: 局部路径规划器
- **terrain_analysis**: 地形分析模块  
- **terrain_analysis_ext**: 扩展地形分析
- **sensor_scan_generation**: 传感器扫描生成
- **vehicle_simulator**: 车辆仿真器
- **loam_interface**: LOAM接口模块
- **visualization_tools**: 可视化工具
- **waypoint_example**: 航点示例
- **waypoint_rviz_plugin**: RViz航点插件

### 2.2 数据流架构

```
传感器数据 → LOAM接口 → 地形分析 → 局部规划器 → 路径跟踪 → 车辆控制
     ↓           ↓          ↓          ↓          ↓
  点云数据   状态估计   地形图    路径规划   速度控制
```

## 3. 关键接口定义

### 3.1 ROS话题接口

#### 输入话题:
- `/state_estimation` (nav_msgs::Odometry): 机器人位姿估计
- `/registered_scan` (sensor_msgs::PointCloud2): 配准后的点云数据
- `/joy` (sensor_msgs::Joy): 手柄控制输入
- `/way_point` (geometry_msgs::PointStamped): 目标航点
- `/speed` (std_msgs::Float32): 速度指令
- `/navigation_boundary` (geometry_msgs::PolygonStamped): 导航边界

#### 输出话题:
- `/terrain_map` (sensor_msgs::PointCloud2): 地形图
- `/path` (nav_msgs::Path): 规划路径
- `/cmd_vel` (geometry_msgs::TwistStamped): 速度控制指令
- `/overall_map` (sensor_msgs::PointCloud2): 全局地图

### 3.2 核心数据结构

#### 路径规划数据结构:
```cpp
const int pathNum = 343;           // 预定义路径数量
const int groupNum = 7;            // 路径组数量
float gridVoxelSize = 0.02;        // 网格体素大小
const int gridVoxelNumX = 161;     // X方向网格数
const int gridVoxelNumY = 451;     // Y方向网格数

int pathList[pathNum];             // 路径列表
int clearPathList[36 * pathNum];   // 清晰路径列表(36个旋转方向)
float pathPenaltyList[36 * pathNum]; // 路径惩罚值
std::vector<int> correspondences[gridVoxelNum]; // 网格-路径对应关系
```

#### 地形分析数据结构:
```cpp
const int terrainVoxelWidth = 251;  // 地形体素宽度
const int terrainVoxelNum = terrainVoxelWidth * terrainVoxelWidth;
const int planarVoxelWidth = 51;    // 平面体素宽度

pcl::PointCloud<pcl::PointXYZI>::Ptr terrainVoxelCloud[terrainVoxelNum];
float terrainVoxelElev[terrainVoxelNum];     // 地形高程
int terrainVoxelUpdateNum[terrainVoxelNum];  // 更新次数
float terrainVoxelUpdateTime[terrainVoxelNum]; // 更新时间
```

## 4. 核心算法实现

### 4.1 局部路径规划算法

#### 预计算路径集合:
- 系统预先计算343条候选路径，分为7个组
- 每条路径在36个旋转方向(每10度一个)下进行评估
- 使用网格化方法进行快速碰撞检测

#### 路径评估流程:
1. **障碍物检测**: 将点云数据投影到网格中
2. **路径筛选**: 检查每条路径与障碍物的碰撞
3. **路径评分**: 基于方向偏差、旋转权重和惩罚值计算分数
4. **路径选择**: 选择最高分数的可行路径组

#### 评分函数:
```cpp
float score = (1 - sqrt(sqrt(dirWeight * dirDiff))) * 
              rotDirW * rotDirW * rotDirW * rotDirW * penaltyScore;
```

### 4.2 地形分析算法

#### 体素化地形表示:
- 使用3D体素网格存储地形信息
- 每个体素记录高程、更新时间和点数
- 支持动态更新和时间衰减

#### 可通行性分析:
1. **高程计算**: 使用分位数方法计算地面高程
2. **障碍物检测**: 基于相对高度阈值识别障碍物
3. **动态障碍物处理**: 检测和清除移动障碍物
4. **地形连通性**: 分析地形的连通性和可达性

## 5. 算法功能裁剪方案

如果只使用算法功能而不进行仿真，可以按以下方式裁剪：

### 5.1 保留的核心模块
- **local_planner**: 路径规划核心算法
- **terrain_analysis**: 地形分析算法
- **loam_interface**: 数据接口(可选择性保留)

### 5.2 可移除的仿真模块
- **vehicle_simulator**: 完全移除
- **sensor_scan_generation**: 移除仿真相关部分
- **velodyne_simulator**: 完全移除
- **visualization_tools**: 保留核心功能，移除仿真可视化

### 5.3 接口适配修改

#### 数据输入接口:
```cpp
// 替换仿真数据源为实际传感器数据
// 原: /velodyne_points (仿真激光雷达)
// 改: /velodyne_points (实际激光雷达)

// 原: /state_estimation (仿真位姿)  
// 改: /odometry/filtered (实际SLAM输出)
```

#### 移除的依赖:
- Gazebo相关依赖
- 仿真世界文件
- 虚拟传感器模型
- 仿真车辆模型

### 5.4 配置参数调整
```yaml
# 关闭仿真相关功能
use_gazebo_time: false
adjustZ: false          # 关闭Z轴自动调整
adjustIncl: false       # 关闭倾斜角调整

# 调整传感器参数适配实际硬件
vehicleHeight: [实际车辆高度]
sensorOffsetX: [实际传感器偏移]
sensorOffsetY: [实际传感器偏移]
```

### 5.5 启动文件修改
创建新的启动文件，只启动算法相关节点：
```python
# 算法模式启动文件
start_local_planner
start_terrain_analysis  
start_loam_interface    # 可选
# 移除: start_vehicle_simulator
# 移除: start_sensor_scan_generation (仿真部分)
```

## 6. 部署建议

### 6.1 硬件要求
- 激光雷达(如Velodyne VLP-16)
- IMU传感器
- 计算平台(支持ROS2)
- 实际移动机器人平台

### 6.2 软件依赖
保留的依赖:
- ROS2 (rclcpp, sensor_msgs, nav_msgs等)
- PCL (点云处理)
- 实际SLAM系统(如LOAM, LeGO-LOAM等)

移除的依赖:
- Gazebo
- gazebo_msgs
- 仿真相关插件

这种裁剪方案可以显著减少系统复杂度，专注于核心算法功能，适合在实际机器人平台上部署。

## 7. 详细算法实现分析

### 7.1 路径规划算法详细流程

#### 预处理阶段:
1. **路径文件加载**:
   - `startPaths.ply`: 起始路径点集合
   - `paths.ply`: 完整路径点集合
   - `pathList.ply`: 路径-组映射关系
   - `correspondences.txt`: 网格-路径对应关系

2. **网格化碰撞检测系统**:
```cpp
// 网格参数
float gridVoxelSize = 0.02;        // 2cm网格精度
float gridVoxelOffsetX = 3.2;      // X方向偏移
float gridVoxelOffsetY = 4.5;      // Y方向偏移
const int gridVoxelNumX = 161;     // 3.2m范围
const int gridVoxelNumY = 451;     // 9m范围

// 点云到网格映射
int indX = int((gridVoxelOffsetX + gridVoxelSize/2 - x2) / gridVoxelSize);
int indY = int((gridVoxelOffsetY + gridVoxelSize/2 - y2/scaleY) / gridVoxelSize);
int ind = gridVoxelNumY * indX + indY;
```

#### 实时规划循环:
1. **点云预处理**:
   - 坐标系转换到车体坐标系
   - 距离和高度过滤
   - 体素化降采样

2. **多尺度路径搜索**:
```cpp
// 自适应路径缩放
while (pathScale >= minPathScale && pathRange >= minPathRange) {
    // 尝试当前尺度下的路径规划
    if (pathFound) break;

    // 缩小路径尺度或范围
    if (pathScale >= minPathScale + pathScaleStep) {
        pathScale -= pathScaleStep;
    } else {
        pathRange -= pathRangeStep;
    }
}
```

3. **36方向路径评估**:
   - 每10度一个方向，覆盖360度
   - 考虑车辆运动学约束
   - 支持前进/后退双向驾驶

### 7.2 地形分析算法详细实现

#### 多层次地形表示:
1. **3D体素网格** (terrainVoxelCloud):
   - 尺寸: 251×251×高度自适应
   - 分辨率: 可配置(默认0.2m)
   - 存储: 点云、高程、时间戳

2. **2D平面网格** (planarVoxelElev):
   - 尺寸: 51×51
   - 用于快速地面高程查询
   - 支持动态障碍物检测

#### 地形更新算法:
```cpp
// 体素更新条件
if (terrainVoxelUpdateNum[ind] < voxelPointUpdateThre ||
    laserCloudTime - systemInitTime - terrainVoxelUpdateTime[ind] > voxelTimeUpdateThre) {

    // 执行体素更新
    // 1. 添加新点云数据
    // 2. 降采样处理
    // 3. 时间衰减过滤
    // 4. 更新统计信息
}
```

#### 可通行性评估:
1. **高程计算**:
   - 使用分位数方法(quantileZ=0.25)
   - 考虑点云密度和分布
   - 处理稀疏数据情况

2. **障碍物分类**:
```cpp
float disZ = point.z - groundElevation;
if (disZ >= 0 && disZ < vehicleHeight) {
    // 标记为障碍物
    terrainCloudElev->points[i].intensity = disZ;
}
```

### 7.3 路径跟踪控制算法

#### PID控制器实现:
```cpp
// 横向控制
float lateralError = crossTrackError;
float yawRateCmd = kp * lateralError + ki * lateralErrorIntegral + kd * lateralErrorDerivative;

// 纵向控制
float speedError = targetSpeed - currentSpeed;
float speedCmd = kp_speed * speedError;
```

#### 路径点跟踪:
1. **最近点搜索**: 在路径上找到距离车辆最近的点
2. **前瞻点计算**: 基于车速计算前瞻距离
3. **横向偏差计算**: 计算车辆到路径的横向距离
4. **航向偏差计算**: 计算车辆航向与路径切线的夹角

## 8. 性能优化策略

### 8.1 计算优化
- **并行处理**: 多线程处理点云数据
- **内存池**: 预分配点云内存避免频繁分配
- **空间索引**: 使用KD-tree加速最近邻搜索
- **增量更新**: 只更新变化的地形区域

### 8.2 实时性保证
- **分层处理**: 高频局部规划 + 低频全局规划
- **异步处理**: 传感器数据处理与规划解耦
- **缓存机制**: 缓存计算结果避免重复计算

## 9. 算法参数调优指南

### 9.1 路径规划参数
```yaml
# 基础参数
vehicleLength: 0.6      # 车辆长度(m)
vehicleWidth: 0.6       # 车辆宽度(m)
adjacentRange: 3.5      # 感知范围(m)
pathScale: 1.0          # 路径缩放因子
minPathScale: 0.75      # 最小路径缩放

# 障碍物检测
obstacleHeightThre: 0.2 # 障碍物高度阈值(m)
groundHeightThre: 0.1   # 地面高度阈值(m)
pointPerPathThre: 2     # 路径阻塞点数阈值

# 方向控制
dirWeight: 0.02         # 方向权重
dirThre: 90.0          # 方向阈值(度)
```

### 9.2 地形分析参数
```yaml
# 体素化参数
scanVoxelSize: 0.05     # 扫描体素大小(m)
terrainVoxelSize: 0.2   # 地形体素大小(m)

# 时间衰减
decayTime: 2.0          # 衰减时间(s)
noDecayDis: 4.0         # 无衰减距离(m)

# 高程计算
quantileZ: 0.25         # 高程分位数
vehicleHeight: 1.5      # 车辆高度(m)
```

## 10. 故障诊断与调试

### 10.1 常见问题
1. **路径规划失败**: 检查障碍物阈值设置
2. **地形图不更新**: 检查传感器数据频率
3. **路径震荡**: 调整方向权重和阈值
4. **计算延迟**: 优化点云处理参数

### 10.2 调试工具
- RViz可视化: 查看路径、地形图、障碍物
- 日志分析: 监控算法执行时间和状态
- 参数动态调整: 运行时修改算法参数
