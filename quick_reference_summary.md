# 自主探索系统快速参考总结

## 📋 系统概览

**项目性质**: 基于ROS2的地面机器人自主导航和探索系统  
**核心功能**: 复杂地形环境下的路径规划、避障和地形分析  
**架构特点**: 模块化设计，支持仿真和实际部署  

## 🏗️ 核心模块一览表

| 模块名称 | 功能描述 | 裁剪建议 | 重要性 |
|---------|---------|---------|--------|
| **local_planner** | 局部路径规划核心算法 | ✅ **必须保留** | ⭐⭐⭐⭐⭐ |
| **terrain_analysis** | 地形可通行性分析 | ✅ **必须保留** | ⭐⭐⭐⭐⭐ |
| **terrain_analysis_ext** | 扩展地形分析功能 | 🔶 可选保留 | ⭐⭐⭐ |
| **loam_interface** | SLAM数据接口 | 🔶 可选保留 | ⭐⭐⭐ |
| **vehicle_simulator** | 车辆仿真器 | ❌ **完全移除** | ⭐ |
| **sensor_scan_generation** | 传感器扫描生成 | ❌ **大部分移除** | ⭐ |
| **velodyne_simulator** | 激光雷达仿真 | ❌ **完全移除** | ⭐ |
| **visualization_tools** | 可视化工具 | 🔶 部分保留 | ⭐⭐ |

## 🔧 关键算法特性

### 路径规划算法 (local_planner)
- **预计算路径集**: 343条候选路径，7个路径组
- **多方向评估**: 36个旋转方向(每10度)
- **网格化碰撞检测**: 161×451网格，2cm精度
- **自适应缩放**: 根据速度和环境动态调整路径尺度
- **实时性能**: >10Hz规划频率

### 地形分析算法 (terrain_analysis)
- **3D体素化**: 251×251×H体素网格表示
- **动态更新**: 支持时间衰减和增量更新
- **可通行性评估**: 基于高程差和点云密度
- **障碍物分类**: 静态/动态障碍物区分
- **内存效率**: 稀疏存储和智能缓存

## 📡 核心接口定义

### 输入接口
```yaml
/state_estimation:     nav_msgs/Odometry      # 机器人位姿
/registered_scan:      sensor_msgs/PointCloud2 # 配准点云
/joy:                  sensor_msgs/Joy         # 手柄控制
/way_point:           geometry_msgs/PointStamped # 目标点
/speed:               std_msgs/Float32         # 速度指令
```

### 输出接口
```yaml
/terrain_map:         sensor_msgs/PointCloud2  # 地形图
/path:               nav_msgs/Path             # 规划路径
/cmd_vel:            geometry_msgs/TwistStamped # 控制指令
```

## ⚙️ 关键参数配置

### 路径规划参数
```yaml
pathNum: 343              # 预定义路径数量
groupNum: 7               # 路径组数量
gridVoxelSize: 0.02       # 网格精度(m)
adjacentRange: 3.5        # 感知范围(m)
obstacleHeightThre: 0.2   # 障碍物高度阈值(m)
pathScale: 1.0            # 路径缩放因子
dirWeight: 0.02           # 方向权重
```

### 地形分析参数
```yaml
terrainVoxelWidth: 251    # 地形网格宽度
scanVoxelSize: 0.05       # 扫描体素大小(m)
decayTime: 2.0           # 数据衰减时间(s)
vehicleHeight: 1.5        # 车辆高度(m)
quantileZ: 0.25          # 高程分位数
```

## 🚀 快速裁剪步骤

### 1. 移除仿真模块
```bash
# 删除仿真相关包
rm -rf src/vehicle_simulator
rm -rf src/velodyne_simulator
rm -rf src/sensor_scan_generation  # 可选择性保留
```

### 2. 修改编译配置
```cmake
# CMakeLists.txt - 移除Gazebo依赖
# find_package(gazebo_msgs REQUIRED)  # 注释掉
# find_package(gazebo_ros REQUIRED)   # 注释掉
```

### 3. 创建算法启动文件
```python
# algorithm_only.launch.py
Node(package='local_planner', executable='localPlanner'),
Node(package='terrain_analysis', executable='terrainAnalysis'),
Node(package='local_planner', executable='pathFollower'),
```

### 4. 调整配置参数
```yaml
# 关闭仿真功能
adjustZ: false
adjustIncl: false
use_gazebo_time: false
autonomyMode: true
```

## 📊 性能指标

### 计算性能
- **路径规划频率**: >10Hz
- **地形更新频率**: >2Hz  
- **内存使用**: <2GB
- **CPU使用**: <50% (单核)

### 算法精度
- **路径精度**: 2cm网格分辨率
- **障碍物检测**: 0.2m高度阈值
- **地形分辨率**: 0.05-0.2m可配置
- **实时响应**: <100ms延迟

## 🔍 故障排查清单

### 常见问题
- [ ] **路径规划失败**: 检查`obstacleHeightThre`设置
- [ ] **地形图不更新**: 验证`/registered_scan`话题
- [ ] **路径震荡**: 调整`dirWeight`和`dirThre`
- [ ] **计算延迟**: 优化`scanVoxelSize`参数
- [ ] **内存泄漏**: 检查点云内存管理

### 调试命令
```bash
# 检查节点状态
ros2 node list
ros2 topic list

# 监控性能
ros2 topic hz /terrain_map
ros2 topic hz /path
ros2 topic delay /registered_scan /path

# 参数调试
ros2 param list /localPlanner
ros2 param set /localPlanner obstacleHeightThre 0.3
```

## 📈 优化建议

### 实时性优化
1. **并行处理**: 多线程处理点云数据
2. **内存池**: 预分配避免频繁内存分配
3. **增量更新**: 只更新变化区域
4. **空间索引**: KD-tree加速邻域搜索

### 精度优化
1. **传感器标定**: 精确的激光雷达-IMU标定
2. **参数调优**: 根据实际环境调整阈值
3. **多传感器融合**: 结合相机、超声波等
4. **机器学习**: 基于历史数据优化参数

## 🎯 部署检查清单

### 硬件准备
- [ ] 激光雷达(Velodyne VLP-16或同等)
- [ ] IMU传感器(高精度9轴)
- [ ] 计算平台(支持ROS2)
- [ ] 移动机器人底盘
- [ ] 电源管理系统

### 软件准备
- [ ] ROS2安装和配置
- [ ] PCL库安装
- [ ] SLAM系统(LeGO-LOAM/A-LOAM)
- [ ] 算法模块编译
- [ ] 参数文件配置

### 测试验证
- [ ] 传感器数据正常
- [ ] 算法节点启动成功
- [ ] 路径规划功能正常
- [ ] 地形分析输出正确
- [ ] 控制指令响应及时
- [ ] 系统稳定性测试

## 📚 相关资源

- **项目主页**: https://www.cmu-exploration.com
- **ROS2文档**: https://docs.ros.org/en/humble/
- **PCL教程**: https://pcl.readthedocs.io/
- **SLAM算法**: LeGO-LOAM, A-LOAM, LIO-SAM

---
**总结**: 该系统通过裁剪可以减少60-70%的复杂度，保留核心算法功能，非常适合实际机器人平台部署。关键是保留`local_planner`和`terrain_analysis`两个核心模块，移除所有仿真相关组件。
