# waypoint_example模块使用指南

## 1. 快速开始

### 1.1 基本使用
```bash
# 启动waypoint_example节点
ros2 launch waypoint_example waypoint_example_garage.launch

# 查看发布的话题
ros2 topic list | grep -E "(way_point|speed|navigation_boundary)"

# 监控当前目标航点
ros2 topic echo /way_point
```

### 1.2 自定义参数启动
```bash
ros2 run waypoint_example waypointExample --ros-args \
  -p waypoint_file_dir:=/path/to/custom_waypoints.ply \
  -p boundary_file_dir:=/path/to/custom_boundary.ply \
  -p waypointXYRadius:=1.0 \
  -p speed:=1.5 \
  -p waitTime:=2.0
```

## 2. 数据样例详解

### 2.1 原始车库样例分析

#### 航点路径分析 (`waypoints_garage.ply`):
```
航点序列:
0: (10.0, 28.0, 0.8) → 起始点
1: (4.0, 69.0, 0.8)  → 向北移动41m
2: (40.0, 69.0, 1.7) → 向东移动36m，上升0.9m
3: (40.0, 16.0, 4.3) → 向南移动53m，上升2.6m
4: (49.0, 16.0, 4.3) → 向东移动9m，保持高度
5: (49.0, 78.0, 1.7) → 向北移动62m，下降2.6m
6: (-1.0, 78.0, 0.8) → 向西移动50m，下降0.9m
7: (10.0, 28.0, 0.8) → 回到起始点，向南移动50m
8: (0, 0, 0.8)       → 最终停止点
```

#### 路径特征:
- **总长度**: 约251米
- **高度变化**: 0.8m - 4.3m (变化3.5m)
- **路径类型**: 复杂多边形路径，包含高度变化
- **应用场景**: 多层车库或复杂室内环境

#### 边界区域分析 (`boundary_garage.ply`):
```
边界形状: L型区域
外边界: (-7.0, -4.0) 到 (54.5, 84.0)
内凹区域: (24.0, 8.0) 到 (28.0, 70.0)
总面积: 约4800平方米
安全边界: 距离航点最近1-2米
```

### 2.2 数据格式标准

#### PLY文件头格式:
```
ply                    # PLY格式标识
format ascii 1.0       # ASCII格式，版本1.0
element vertex N       # N为顶点数量
property float x       # X坐标属性
property float y       # Y坐标属性
property float z       # Z坐标属性
end_header            # 头部结束标记
```

#### 坐标数据格式:
```
X坐标    Y坐标    Z坐标
10.0     28.0     0.8     # 制表符或空格分隔
4.0      69.0     0.8     # 浮点数格式
...
```

## 3. 自定义数据创建

### 3.1 使用数据生成工具

#### 生成圆形路径:
```bash
python3 waypoint_data_generator.py \
  --type circle \
  --circle-center-x 0 \
  --circle-center-y 0 \
  --circle-radius 15 \
  --circle-points 12 \
  --prefix circle_15m
```

#### 生成矩形路径:
```bash
python3 waypoint_data_generator.py \
  --type rectangle \
  --rect-x-min -20 \
  --rect-y-min -10 \
  --rect-x-max 20 \
  --rect-y-max 10 \
  --prefix rect_40x20
```

#### 生成之字形路径:
```bash
python3 waypoint_data_generator.py \
  --type zigzag \
  --zigzag-x-start -10 \
  --zigzag-y-start 0 \
  --zigzag-x-end 10 \
  --zigzag-y-end 20 \
  --zigzag-rows 6 \
  --zigzag-spacing 3 \
  --prefix zigzag_survey
```

### 3.2 手动创建数据文件

#### 简单直线路径示例:
```python
# simple_line_waypoints.ply
waypoints = [
    (0.0, 0.0, 0.5),    # 起点
    (5.0, 0.0, 0.5),    # 5米直线
    (10.0, 0.0, 0.5),   # 10米直线
    (15.0, 0.0, 0.5),   # 15米直线
    (20.0, 0.0, 0.5),   # 终点
]

# 对应的矩形边界
boundary = [
    (-2.0, -2.0, 0),    # 左下
    (-2.0, 2.0, 0),     # 左上
    (22.0, 2.0, 0),     # 右上
    (22.0, -2.0, 0),    # 右下
    (-2.0, -2.0, 0),    # 闭合
]
```

#### 室内导航路径示例:
```python
# indoor_navigation_waypoints.ply
indoor_waypoints = [
    (0.0, 0.0, 0.3),     # 房间1入口
    (3.0, 0.0, 0.3),     # 房间1中央
    (3.0, 4.0, 0.3),     # 走廊入口
    (8.0, 4.0, 0.3),     # 走廊中段
    (8.0, 8.0, 0.3),     # 房间2入口
    (12.0, 8.0, 0.3),    # 房间2中央
    (12.0, 4.0, 0.3),    # 返回走廊
    (3.0, 4.0, 0.3),     # 走廊入口
    (0.0, 0.0, 0.3),     # 回到起点
]
```

## 4. 参数调优指南

### 4.1 到达检测参数

#### waypointXYRadius (水平到达半径):
```yaml
# 室内精确导航
waypointXYRadius: 0.3    # 30cm精度

# 室外粗略导航  
waypointXYRadius: 1.0    # 1m精度

# 大范围探索
waypointXYRadius: 2.0    # 2m精度
```

#### waypointZBound (垂直到达范围):
```yaml
# 平地导航
waypointZBound: 1.0      # 1m垂直范围

# 多层环境
waypointZBound: 5.0      # 5m垂直范围

# 严格高度控制
waypointZBound: 0.5      # 50cm垂直范围
```

### 4.2 时间控制参数

#### waitTime (航点等待时间):
```yaml
# 连续导航
waitTime: 0.0           # 无等待

# 数据采集任务
waitTime: 5.0           # 每点等待5秒

# 检查任务
waitTime: 10.0          # 每点等待10秒
```

#### frameRate (发布频率):
```yaml
# 高精度控制
frameRate: 10.0         # 10Hz

# 标准控制
frameRate: 5.0          # 5Hz

# 低频控制
frameRate: 2.0          # 2Hz
```

### 4.3 速度控制参数

#### speed (期望速度):
```yaml
# 室内慢速
speed: 0.5              # 0.5 m/s

# 标准速度
speed: 1.0              # 1.0 m/s

# 室外快速
speed: 2.0              # 2.0 m/s
```

## 5. 集成使用示例

### 5.1 与导航系统集成

#### 完整导航启动文件:
```xml
<launch>
  <!-- 启动SLAM -->
  <include file="$(find-pkg-share loam_interface)/launch/loam_interface.launch" />
  
  <!-- 启动地形分析 -->
  <include file="$(find-pkg-share terrain_analysis)/launch/terrain_analysis.launch" />
  
  <!-- 启动路径规划 -->
  <include file="$(find-pkg-share local_planner)/launch/local_planner.launch" />
  
  <!-- 启动航点管理 -->
  <node pkg="waypoint_example" exec="waypointExample" name="waypointExample">
    <param name="waypoint_file_dir" value="$(find-pkg-share waypoint_example)/data/custom_waypoints.ply" />
    <param name="boundary_file_dir" value="$(find-pkg-share waypoint_example)/data/custom_boundary.ply" />
    <param name="waypointXYRadius" value="0.8" />
    <param name="speed" value="1.5" />
    <param name="frameRate" value="5.0" />
  </node>
  
  <!-- 启动路径跟踪 -->
  <include file="$(find-pkg-share local_planner)/launch/path_follower.launch" />
</launch>
```

### 5.2 任务特定配置

#### 巡检任务配置:
```yaml
# 巡检任务参数
waypointXYRadius: 1.0    # 较大到达半径
waypointZBound: 2.0      # 适中垂直范围
waitTime: 3.0           # 每点停留3秒进行检查
speed: 1.0              # 中等速度
frameRate: 5.0          # 标准频率
sendBoundary: true      # 启用边界约束
```

#### 测绘任务配置:
```yaml
# 测绘任务参数
waypointXYRadius: 0.5    # 高精度到达
waypointZBound: 1.0      # 严格高度控制
waitTime: 5.0           # 每点停留5秒采集数据
speed: 0.8              # 较慢速度保证数据质量
frameRate: 10.0         # 高频率控制
sendBoundary: true      # 启用边界约束
```

#### 快速运输配置:
```yaml
# 运输任务参数
waypointXYRadius: 1.5    # 较大到达半径
waypointZBound: 3.0      # 宽松垂直范围
waitTime: 1.0           # 短暂停留
speed: 2.5              # 高速移动
frameRate: 5.0          # 标准频率
sendBoundary: true      # 启用安全边界
```

## 6. 调试和故障排除

### 6.1 常见问题

#### 航点不切换:
```bash
# 检查到达参数
ros2 param get /waypointExample waypointXYRadius
ros2 param get /waypointExample waypointZBound

# 监控距离
ros2 topic echo /state_estimation | grep position
ros2 topic echo /way_point | grep point

# 调整参数
ros2 param set /waypointExample waypointXYRadius 1.0
```

#### 路径不发布:
```bash
# 检查文件路径
ros2 param get /waypointExample waypoint_file_dir

# 检查文件格式
head -10 /path/to/waypoints.ply

# 查看日志
ros2 node info /waypointExample
```

### 6.2 性能监控

#### 监控话题频率:
```bash
ros2 topic hz /way_point        # 应该接近frameRate
ros2 topic hz /speed           # 应该接近frameRate
ros2 topic hz /navigation_boundary  # 应该接近frameRate
```

#### 监控系统状态:
```bash
# 查看节点状态
ros2 node list | grep waypoint

# 查看参数
ros2 param list /waypointExample

# 查看话题连接
ros2 topic info /way_point
```

## 7. 最佳实践

### 7.1 航点设计原则
- **间距合理**: 航点间距2-5米，避免过密或过疏
- **高度连续**: 相邻航点高度变化<1米
- **转弯平滑**: 避免急转弯，使用圆弧过渡
- **边界安全**: 航点距离边界至少1米

### 7.2 参数设置建议
- **到达半径**: 设为机器人尺寸的1-2倍
- **发布频率**: 与控制系统频率匹配
- **等待时间**: 根据任务需求设置
- **速度设置**: 考虑安全性和效率平衡

这个模块为自主导航系统提供了灵活的航点管理功能，通过合理的参数配置和数据设计，可以适应各种导航任务需求。
