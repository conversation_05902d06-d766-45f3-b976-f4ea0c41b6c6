<?xml version="1.0" ?>
<sdf version="1.6">
  <world name="default">
    <include>
      <uri>model://sun</uri>
    </include>
    <include>
      <uri>model://garage</uri>
    </include>
    <plugin name="ros_interface_plugin" filename="librotors_gazebo_ros_interface_plugin.so"/>
    <plugin name="gazebo_ros_state" filename="libgazebo_ros_state.so"/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>47.3667</latitude_deg>
      <longitude_deg>8.5500</longitude_deg>
      <elevation>500.0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <physics type='ode'>
      <ode>
        <solver>
          <type>quick</type>
          <iters>1000</iters>
          <sor>1.3</sor>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
      <gravity>0 0 0</gravity>
    </physics>
  </world>
</sdf>
