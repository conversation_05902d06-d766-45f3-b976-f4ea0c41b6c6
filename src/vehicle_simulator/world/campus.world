<sdf version='1.6'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose frame=''>0 0 300 0 0 0</pose>
      <diffuse>1 1 1 1</diffuse>
      <specular>0 0 0 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>1</constant>
        <linear>0</linear>
        <quadratic>0</quadratic>
      </attenuation>
      <direction>0 1 -1</direction>
    </light>
    <physics name='default_physics' default='0' type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.8 0.8 0.8 1</ambient>
      <background>0.3 0.3 0.3 1</background>
      <shadows>0</shadows>
    </scene>
    <plugin name="ros_interface_plugin" filename="librotors_gazebo_ros_interface_plugin.so"/>
    <plugin name="gazebo_ros_state" filename="libgazebo_ros_state.so"/>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <state world_name='default'>
      <sim_time>4176 536000000</sim_time>
      <real_time>49 494850140</real_time>
      <wall_time>1541177620 221891024</wall_time>
      <iterations>48963</iterations>
      <model name='map'>
        <pose frame=''>0 0 0 0 0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0 0 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <light name='sun'>
        <pose frame=''>0 0 300 0 0 0</pose>
      </light>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose frame=''>0 0 150 0 1.5708 0</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
    <gravity>0 0 0</gravity>
    <magnetic_field>0 0 0</magnetic_field>
    <atmosphere type='adiabatic'/>
    <model name='map'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://campus/meshes/campus.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <torsional>
                <ode/>
              </torsional>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://campus/meshes/campus.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
      <pose frame=''>0 0 1 0 0 0</pose>
    </model>
  </world>
</sdf>
