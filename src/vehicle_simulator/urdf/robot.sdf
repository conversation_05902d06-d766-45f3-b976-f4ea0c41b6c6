<sdf version='1.7'>
  <model name='example'>
    <link name='base_origin'>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>1.0</mass>
        <inertia>
          <ixx>0.1</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.1</iyy>
          <iyz>0</iyz>
          <izz>0.1</izz>
        </inertia>
      </inertial>
      <visual name='base_origin_visual'>
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
            <size>0.01 0.01 0.01</size>
          </box>
        </geometry>
      </visual>
      <visual name='base_origin_fixed_joint_lump__base_link_visual_1'>
        <pose>0 0 -0.5 0 0 0</pose>
        <geometry>
          <box>
            <size>0.6 0.4 0.3</size>
          </box>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='base_origin_fixed_joint_lump__left_front_wheel_visual_2'>
        <pose>0.2 -0.25 -0.6 1.57 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.1</length>
            <radius>0.15</radius>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='base_origin_fixed_joint_lump__left_rear_wheel_visual_3'>
        <pose>-0.2 -0.25 -0.6 1.57 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.1</length>
            <radius>0.15</radius>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='base_origin_fixed_joint_lump__right_rear_wheel_visual_4'>
        <pose>-0.2 0.25 -0.6 -1.57 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.1</length>
            <radius>0.15</radius>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='base_origin_fixed_joint_lump__right_front_wheel_visual_5'>
        <pose>0.2 0.25 -0.6 -1.57 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.1</length>
            <radius>0.15</radius>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='base_origin_fixed_joint_lump__lidar_holder_visual_6'>
        <pose>0 0 -0.175 0 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.35</length>
            <radius>0.03</radius>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
    </link>
  </model>
</sdf>
