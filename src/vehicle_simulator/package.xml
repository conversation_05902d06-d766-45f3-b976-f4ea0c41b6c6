<package format="3">
  <name>vehicle_simulator</name>
  <version>0.0.1</version>
  <description>Vehicle Simulator</description>
  <maintainer email="zhang<PERSON>@cmu.edu"><PERSON></maintainer>
  <license>BSD</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  
  <depend>rclcpp</depend>
  <depend>std_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>pcl_ros</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>message_filters</depend>
  <depend>pcl_conversions</depend>
  <depend>gazebo_ros</depend>
  <depend>gazebo_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <gazebo_ros gazebo_model_path="${prefix}/mesh"/>
  </export>
</package>
