<package format="3">
  <name>sensor_scan_generation</name>
  <version>0.0.1</version>
  <description>sensor_scan_generation is a package to generate scan data in sensor frame with registered_scan in map frame.</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <license>BSD</license>

  <author><PERSON><PERSON><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  
  <depend>rclcpp</depend>
  <depend>std_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>pcl_ros</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>message_filters</depend>
  <depend>pcl_conversions</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
