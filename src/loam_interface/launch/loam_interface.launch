<launch>

  <node pkg="loam_interface" exec="loamInterface" name="loamInterface" output="screen">
    <param name="stateEstimationTopic" value="/integrated_to_init" />
    <param name="registeredScanTopic" value="/velodyne_cloud_registered" />
    <param name="flipStateEstimation" value="true" />
    <param name="flipRegisteredScan" value="true" />
    <param name="sendTF" value="true" />
    <param name="reverseTF" value="false" />
  </node>

</launch>
