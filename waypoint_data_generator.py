#!/usr/bin/env python3
"""
waypoint_example数据样例生成工具
用于生成PLY格式的航点文件和边界文件

作者: 自主探索系统分析
日期: 2025-08-01
"""

import math
import argparse
import os

class WaypointDataGenerator:
    """航点数据生成器"""
    
    def __init__(self):
        self.waypoints = []
        self.boundary_points = []
    
    def add_waypoint(self, x, y, z):
        """添加航点"""
        self.waypoints.append((float(x), float(y), float(z)))
    
    def add_boundary_point(self, x, y, z=0.0):
        """添加边界点"""
        self.boundary_points.append((float(x), float(y), float(z)))
    
    def generate_circle_waypoints(self, center_x, center_y, radius, num_points, height=0.8):
        """生成圆形航点路径"""
        self.waypoints.clear()
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            self.add_waypoint(x, y, height)
        
        # 回到起点
        if num_points > 0:
            self.add_waypoint(self.waypoints[0][0], self.waypoints[0][1], height)
    
    def generate_rectangle_waypoints(self, x_min, y_min, x_max, y_max, height=0.8):
        """生成矩形航点路径"""
        self.waypoints.clear()
        
        # 矩形四个角点
        corners = [
            (x_min, y_min, height),  # 左下
            (x_min, y_max, height),  # 左上
            (x_max, y_max, height),  # 右上
            (x_max, y_min, height),  # 右下
            (x_min, y_min, height)   # 回到起点
        ]
        
        for corner in corners:
            self.add_waypoint(*corner)
    
    def generate_zigzag_waypoints(self, x_start, y_start, x_end, y_end, 
                                 num_rows, row_spacing, height=0.8):
        """生成之字形航点路径"""
        self.waypoints.clear()
        
        for i in range(num_rows):
            y = y_start + i * row_spacing
            if i % 2 == 0:  # 偶数行：从左到右
                self.add_waypoint(x_start, y, height)
                self.add_waypoint(x_end, y, height)
            else:  # 奇数行：从右到左
                self.add_waypoint(x_end, y, height)
                self.add_waypoint(x_start, y, height)
    
    def generate_rectangle_boundary(self, x_min, y_min, x_max, y_max, margin=1.0):
        """生成矩形边界"""
        self.boundary_points.clear()
        
        # 添加边界点(逆时针)
        boundary = [
            (x_min - margin, y_min - margin, 0),  # 左下
            (x_min - margin, y_max + margin, 0),  # 左上
            (x_max + margin, y_max + margin, 0),  # 右上
            (x_max + margin, y_min - margin, 0),  # 右下
            (x_min - margin, y_min - margin, 0)   # 闭合
        ]
        
        for point in boundary:
            self.add_boundary_point(*point)
    
    def generate_l_shape_boundary(self, x_min, y_min, x_max, y_max, 
                                 inner_x, inner_y):
        """生成L形边界"""
        self.boundary_points.clear()
        
        # L形边界点(逆时针)
        boundary = [
            (x_min, y_min, 0),      # 起点
            (x_min, y_max, 0),      # 向上
            (x_max, y_max, 0),      # 向右
            (x_max, inner_y, 0),    # 向下到内凹点
            (inner_x, inner_y, 0),  # 向左内凹
            (inner_x, y_min, 0),    # 向下
            (x_min, y_min, 0)       # 闭合
        ]
        
        for point in boundary:
            self.add_boundary_point(*point)
    
    def save_waypoints_ply(self, filename):
        """保存航点到PLY文件"""
        with open(filename, 'w') as f:
            f.write('ply\n')
            f.write('format ascii 1.0\n')
            f.write(f'element vertex {len(self.waypoints)}\n')
            f.write('property float x\n')
            f.write('property float y\n')
            f.write('property float z\n')
            f.write('end_header\n')
            
            for x, y, z in self.waypoints:
                f.write(f'{x:.1f}\t{y:.1f}\t{z:.1f}\n')
        
        print(f"航点文件已保存: {filename}")
        print(f"航点数量: {len(self.waypoints)}")
    
    def save_boundary_ply(self, filename):
        """保存边界到PLY文件"""
        with open(filename, 'w') as f:
            f.write('ply\n')
            f.write('format ascii 1.0\n')
            f.write(f'element vertex {len(self.boundary_points)}\n')
            f.write('property float x\n')
            f.write('property float y\n')
            f.write('property float z\n')
            f.write('end_header\n')
            
            for x, y, z in self.boundary_points:
                f.write(f'{x:.1f}\t{y:.1f}\t{z:.1f}\n')
        
        print(f"边界文件已保存: {filename}")
        print(f"边界点数量: {len(self.boundary_points)}")
    
    def print_waypoints(self):
        """打印航点信息"""
        print("\n=== 航点列表 ===")
        for i, (x, y, z) in enumerate(self.waypoints):
            print(f"航点 {i}: ({x:.1f}, {y:.1f}, {z:.1f})")
    
    def print_boundary(self):
        """打印边界信息"""
        print("\n=== 边界列表 ===")
        for i, (x, y, z) in enumerate(self.boundary_points):
            print(f"边界点 {i}: ({x:.1f}, {y:.1f}, {z:.1f})")

def create_garage_example():
    """创建车库示例数据(复现原始数据)"""
    generator = WaypointDataGenerator()
    
    # 原始车库航点数据
    garage_waypoints = [
        (10.0, 28.0, 0.8),
        (4.0, 69.0, 0.8),
        (40.0, 69.0, 1.7),
        (40.0, 16.0, 4.3),
        (49.0, 16.0, 4.3),
        (49.0, 78.0, 1.7),
        (-1.0, 78.0, 0.8),
        (10.0, 28.0, 0.8),
        (0, 0, 0.8)
    ]
    
    for wp in garage_waypoints:
        generator.add_waypoint(*wp)
    
    # 原始车库边界数据
    garage_boundary = [
        (-7.0, -4.0, 0),
        (-7.0, 84.0, 0),
        (54.5, 84.0, 0),
        (54.5, 8.0, 0),
        (28.0, 8.0, 0),
        (28.0, 70.0, 0),
        (24.0, 70.0, 0),
        (24.0, -4.0, 0),
        (-7.0, -4.0, 0)
    ]
    
    for bp in garage_boundary:
        generator.add_boundary_point(*bp)
    
    return generator

def main():
    parser = argparse.ArgumentParser(description='航点数据生成工具')
    parser.add_argument('--type', choices=['garage', 'circle', 'rectangle', 'zigzag'], 
                       default='garage', help='生成数据类型')
    parser.add_argument('--output-dir', default='.', help='输出目录')
    parser.add_argument('--prefix', default='custom', help='文件名前缀')
    
    # 圆形路径参数
    parser.add_argument('--circle-center-x', type=float, default=0.0, help='圆心X坐标')
    parser.add_argument('--circle-center-y', type=float, default=0.0, help='圆心Y坐标')
    parser.add_argument('--circle-radius', type=float, default=10.0, help='圆半径')
    parser.add_argument('--circle-points', type=int, default=8, help='圆周航点数')
    
    # 矩形路径参数
    parser.add_argument('--rect-x-min', type=float, default=-10.0, help='矩形最小X')
    parser.add_argument('--rect-y-min', type=float, default=-10.0, help='矩形最小Y')
    parser.add_argument('--rect-x-max', type=float, default=10.0, help='矩形最大X')
    parser.add_argument('--rect-y-max', type=float, default=10.0, help='矩形最大Y')
    
    # 之字形路径参数
    parser.add_argument('--zigzag-x-start', type=float, default=-5.0, help='之字形起始X')
    parser.add_argument('--zigzag-y-start', type=float, default=-5.0, help='之字形起始Y')
    parser.add_argument('--zigzag-x-end', type=float, default=5.0, help='之字形结束X')
    parser.add_argument('--zigzag-y-end', type=float, default=5.0, help='之字形结束Y')
    parser.add_argument('--zigzag-rows', type=int, default=5, help='之字形行数')
    parser.add_argument('--zigzag-spacing', type=float, default=2.0, help='行间距')
    
    args = parser.parse_args()
    
    generator = WaypointDataGenerator()
    
    if args.type == 'garage':
        generator = create_garage_example()
        print("生成车库示例数据")
    
    elif args.type == 'circle':
        generator.generate_circle_waypoints(
            args.circle_center_x, args.circle_center_y, 
            args.circle_radius, args.circle_points
        )
        generator.generate_rectangle_boundary(
            args.circle_center_x - args.circle_radius - 2,
            args.circle_center_y - args.circle_radius - 2,
            args.circle_center_x + args.circle_radius + 2,
            args.circle_center_y + args.circle_radius + 2
        )
        print(f"生成圆形路径: 中心({args.circle_center_x}, {args.circle_center_y}), "
              f"半径{args.circle_radius}, {args.circle_points}个航点")
    
    elif args.type == 'rectangle':
        generator.generate_rectangle_waypoints(
            args.rect_x_min, args.rect_y_min, 
            args.rect_x_max, args.rect_y_max
        )
        generator.generate_rectangle_boundary(
            args.rect_x_min, args.rect_y_min,
            args.rect_x_max, args.rect_y_max, margin=2.0
        )
        print(f"生成矩形路径: ({args.rect_x_min}, {args.rect_y_min}) 到 "
              f"({args.rect_x_max}, {args.rect_y_max})")
    
    elif args.type == 'zigzag':
        generator.generate_zigzag_waypoints(
            args.zigzag_x_start, args.zigzag_y_start,
            args.zigzag_x_end, args.zigzag_y_end,
            args.zigzag_rows, args.zigzag_spacing
        )
        generator.generate_rectangle_boundary(
            args.zigzag_x_start, args.zigzag_y_start,
            args.zigzag_x_end, args.zigzag_y_end, margin=2.0
        )
        print(f"生成之字形路径: {args.zigzag_rows}行, 间距{args.zigzag_spacing}m")
    
    # 保存文件
    waypoint_file = os.path.join(args.output_dir, f'{args.prefix}_waypoints.ply')
    boundary_file = os.path.join(args.output_dir, f'{args.prefix}_boundary.ply')
    
    generator.save_waypoints_ply(waypoint_file)
    generator.save_boundary_ply(boundary_file)
    
    # 打印信息
    generator.print_waypoints()
    generator.print_boundary()
    
    print(f"\n=== 使用方法 ===")
    print(f"在ROS2启动文件中设置:")
    print(f'<param name="waypoint_file_dir" value="{waypoint_file}" />')
    print(f'<param name="boundary_file_dir" value="{boundary_file}" />')

if __name__ == '__main__':
    main()
